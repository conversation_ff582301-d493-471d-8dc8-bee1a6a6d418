# Những quy định liên quan đến đóng góp dành cho kho code của NukeViet
Bằng việc tham gia kho code này ( [coder](#coder) hoặc [tester](#tester)), bạn đồng ý với các quy định dưới đây:

## Coder
### Bản quyền mã nguồn đóng góp
- Các coder cam kết mình là tác giả mã nguồn hoặc có đủ quyền đối với mã nguồn mà mình đóng góp. Điều này có nghĩa là nếu bạn là coder, bạn phải cam kết mình là tác giả của code hoặc code được cung cấp theo một giấy phép tương thích với [giấy phép mà NukeViet đang sử dụng](../LICENSE.txt).
- Ngoại trừ các thành phần code được xây dựng độc lập, có thể tách rời khỏi NukeViet thì có thể được cấp phép theo giấy phép riêng tương thích với [giấy phép mà NukeViet đang sử dụng](../LICENSE.txt) và phải được liệt kê ở [file thông tin bản quyền](../COPYRIGHT.txt).
- Tất cả các chỉnh sửa vào mã nguồn ban đầu hoặc trực tiếp đóng góp cho code NukeViet không phải dưới dạng thành phần có thể tách ra khỏi NukeViet một cách độc lập (theo dạng addons hay plugins) đều được coder cam kết tuân theo [giấy phép mà NukeViet đang sử dụng](../LICENSE.txt). 
- Các coder tham gia đóng góp code cho NukeViet đồng ý rằng VINADES.,JSC hoặc một tổ chức hợp pháp sở hữu thương hiệu NukeViet có quyền thay đổi [giấy phép NukeViet đang sử dụng](../LICENSE.txt) mà không cần xin phép lại các coder về bản quyền các mã nguồn đã đóng góp cho NukeViet.
### Trách nhiệm về bản quyền
- Hệ thống ghi nhận toàn bộ quá trình đóng góp code. Các coder phải hoàn toàn chịu trách nhiệm cá nhân nếu đưa các đoạn mã vi phạm bản quyền vào kho code của NukeViet.
### Ủy quyền quản lý giấy phép
- Các coder tham gia đóng góp code đồng ý rằng sẽ trao quyền cho VINADES.,JSC hoặc một tổ chức hợp pháp đại diện cho cộng đồng NukeViet thay mặt coder giải quyết các vấn đề tranh chấp về bản quyền phần mềm NukeViet (nếu có).

## Tester
### Quy định đóng góp
Tương tự việc đóng góp code, đóng góp của các tester cũng được tuân thủ theo quy định như đối với coder
### Hướng dẫn gửi báo lỗi
Xem [Quy định thông báo lỗi](./ISSUE_TEMPLATE.md)
