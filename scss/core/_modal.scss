.cr-md-backdrop {
  @extend %reboot-all;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--cr-md-zindex-backdrop);
  width: 100vw;
  height: 100vh;
  background-color: var(--cr-md-backdrop-bg);

  &.cr-show {
    opacity: var(--cr-md-backdrop-opacity);
  }
}

.cr-md {
  @extend %reboot-all;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--cr-md-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.cr-md-open {
  .select2-dropdown {
    --#{$prefix}dropdown-zindex: var(--cr-md-zindex);
  }
}

.cr-md-dialog {
  @extend %reboot-all;
  position: relative;
  width: auto;
  pointer-events: none;
  margin: .5rem;
}

@include media-breakpoint-up(sm) {
  .cr-md-dialog {
    max-width: var(--cr-md-md-width);
    margin-right: auto;
    margin-left: auto;
    margin-top: 1.75rem;
    margin-bottom: 1.75rem;
  }
}

.cr-md.cr-fade .cr-md-dialog {
  transition: transform .3s ease-out;
  transform: translate(0, -50px);
}

.cr-md.cr-show .cr-md-dialog {
  transform: none;
}

.cr-md.cr-md-static .cr-md-dialog {
  transform: scale(1.02);
}

.cr-md-content {
  @extend %reboot-all;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: var(--cr-md-content-bg);
  background-clip: padding-box;
  border: 1px solid var(--cr-md-content-border-color);
  border-radius: var(--cr-md-content-border-radius);
  outline: 0;
}

.cr-md-header {
  @extend %reboot-all;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  padding: .5rem 1rem;
  border-bottom: 1px solid var(--cr-border-color);
  border-top-left-radius: var(--cr-md-content-inner-border-radius);
  border-top-right-radius: var(--cr-md-content-inner-border-radius);

  .cr-btn-close {
    padding: .5rem;
    margin-left: auto;
  }
}

.cr-md-title {
  font-size: var(--cr-md-header-font-size);
  font-weight: var(--cr-md-header-font-weight);
}

.cr-md-body {
  @extend %reboot-all;
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}
