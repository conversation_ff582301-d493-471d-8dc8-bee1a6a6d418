/**
 * NukeViet Content Management System
 * @version 5.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

// Define variable
@import "../functions";
@import "variables";
@import "variables-dark";
@import "../../node_modules/bootstrap/scss/functions";
@import "../../node_modules/bootstrap/scss/variables";

$article-indicators: (
    'deactive': '--bs-tertiary-color',
    'draft': '--bs-secondary-color',
    'expired': '--bs-emphasis-color',
    'locking': '--bs-warning-bg-subtle',
    'publish': '--bs-success',
    'publish-checking': '--bs-success-bg-subtle',
    'publish-reject': '--bs-warning-bg-subtle',
    'publish-transfer': '--bs-info-bg-subtle',
    'review-reject': '--bs-warning',
    'review-transfer': '--bs-info',
    'reviewing': '--bs-primary',
    'waiting': '--bs-primary-bg-subtle'
);

#list-news-items {
    --indicator-start-width: 0.125rem;

    tr > td:first-child {
        position: relative;

        &:before {
            content: "";
            display: block;
            width: var(--indicator-start-width);
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
        }

        @each $key, $value in $article-indicators {
            &.indicator-#{$key}:before {
                background-color: var(#{$value});
            }
        }

        &.indicator-publish:before {
            display: none;
        }
    }
}
