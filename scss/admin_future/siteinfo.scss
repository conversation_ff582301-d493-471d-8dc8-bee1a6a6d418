/**
 * NukeViet Content Management System
 * @version 5.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

// Define variable
@import "../functions";
@import "variables";
@import "variables-dark";
@import "../../node_modules/bootstrap/scss/functions";
@import "../../node_modules/bootstrap/scss/variables";

$card-tools-icon-size: 1.25rem; // 20px
$widget-couter-icon-size: 3rem;

.widget {
    height: 100%;

    .couter-icon {
        width: $widget-couter-icon-size;
        height: $widget-couter-icon-size;

        span {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}

.widget-edit {
    min-height: calc($card-tools-icon-size + .5rem);
    height: 100%;

    >.tools {
        &.tool-bottom {
            right: calc(-1 * $card-tools-icon-size / 2);
            transform: translate(0, -50%);
        }

        a.ic {
            width: $card-tools-icon-size;
            height: $card-tools-icon-size;
            box-shadow: var(--bs-box-shadow);
        }

        &.tool-top {
            top: .125rem;
        }
    }

    .dropdown-menu {
        --bs-dropdown-min-width: 11rem;

        .widget-col-select {
            width: 3.75rem;
        }
    }
}

.widget-edit-drop {
    border-width: .0625rem;
    border-style: solid;
    border-color: transparent;

    &.active {
        border-color: var(--bs-danger-border-subtle);
    }

    &.hover {
        border-color: var(--bs-danger);
    }
}

.widget-scroller {
    height: 100%;
    max-height: 18.125rem;
    position: relative;
    overflow: hidden;
    border-bottom-left-radius: calc(var(--bs-border-radius) + .0625rem);
    border-bottom-right-radius: calc(var(--bs-border-radius) + .0625rem);
}

.widget-chart-290 {
    height: 290px;
}

.widget-chart-wait {
    .colholder {
        width: .75rem;
    }
}

.notilist-items {
    td:first-child {
        border-left-width: 0.1875rem;
    }

    .item-unread {
        border-left: 0.1875rem var(--bs-primary) solid;
    }

    .image {
        width: $notification-image-size;
        height: $notification-image-size;

        i {
            font-size: $notification-image-size;
        }

        img {
            object-position: center;
            object-fit: cover;
            width: 100%;
            height: 100%;
        }
    }
}

.notilist-head {
    th:first-child {
        border-left-width: 0.1875rem;
    }
}

