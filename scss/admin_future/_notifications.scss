.site-notis {
    .notification {
        .tools {
            position: absolute;
            right: 1rem;
            top: calc(1rem + $notification-image-size / 2 - .75rem);

            a {
                width: 1.5rem;
                height: 1.5rem;
                background-color: var(--bs-body-bg);
                box-shadow: $box-shadow-sm;
                font-size: .625rem;
                display: none;
                line-height: calc(1.5rem + 2px);
            }
        }

        &.notification-unread {
            --bs-border-color: var(--nv-notification-unread-border-color);

            .noti-item {
                background-color: $notification-unread-bg;
            }
        }

        .user-name {
            color: var(--bs-primary);
        }

        .date {
            font-size: $notification-date-font-size;
            color: var(--bs-secondary);
        }

        .noti-item {
            color: var(--bs-body-color);
        }

        &:hover {
            .noti-item {
                color: color-contrast($primary);
                background-color: var(--bs-primary);

                .user-name {
                    color: color-contrast($primary);
                }

                .date {
                    color: color-contrast($primary);
                }
            }

            .tools {
                a {
                    display: block;
                }
            }
        }

        .image {
            flex-basis: $notification-image-size;
            width: $notification-image-size;
            height: $notification-image-size;

            i {
                font-size: $notification-image-size;
            }

            img {
                object-position: center;
                object-fit: cover;
                width: 100%;
                height: 100%;
            }
        }
    }
}
