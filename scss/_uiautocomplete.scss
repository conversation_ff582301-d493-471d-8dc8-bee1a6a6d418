.ui-menu {
    @include rfs($dropdown-font-size, --#{$prefix}dropdown-font-size);
    margin: 0;
    padding: #{$dropdown-padding-y} 0;
    @include font-size(var(--#{$prefix}dropdown-font-size));
    color: $dropdown-color;
    list-style: none;
    background-clip: padding-box;
    @include border-radius(#{$dropdown-border-radius});
    position: absolute;
    z-index: $zindex-dropdown;
}

.ui-autocomplete {
    max-width: 18.75rem;
}

%ui-state-active {
    color: #{$dropdown-link-active-color};
    text-decoration: none;
    @include gradient-bg(#{$dropdown-link-active-bg});
}

%ui-state-focus {
    color: #{$dropdown-link-hover-color};
    text-decoration: if($link-hover-decoration ==underline, none, null);
    @include gradient-bg(#{$dropdown-link-hover-bg});
}

.ui-menu-item-wrapper {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ui-menu-item {
    padding: #{$dropdown-item-padding-y} #{$dropdown-item-padding-x};
    font-weight: $font-weight-normal;
    color: #{$dropdown-link-color};
    text-decoration: if($link-decoration ==none, null, none);
    cursor: pointer;

    &:hover,
    &:focus {
        @extend %ui-state-focus;
    }

    &.active,
    &:active {
        @extend %ui-state-active;
    }

    &.disabled,
    &:disabled {
        color: #{$dropdown-link-disabled-color};
        pointer-events: none;
        background-color: transparent;
        background-image: if($enable-gradients, none, null);
    }

    .ui-state-active {
        color: $link-color;
    }
}

.ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}
