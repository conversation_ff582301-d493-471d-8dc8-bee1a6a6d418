:root {
    --nv-image-style-spacing: 1.25rem;
    --nv-inline-image-style-spacing: calc(var(--nv-image-style-spacing) / 2);
    --nv-highlight-marker-blue: #{$cyan};
    --nv-highlight-marker-green: #{$teal};
    --nv-highlight-marker-pink: #{$pink};
    --nv-highlight-marker-yellow: #{$yellow};
    --nv-highlight-pen-green: #{$green};
    --nv-highlight-pen-red: #{$red};
    --nv-font-size-xs: calc(#{$font-size-base} - .125rem);
    --nv-font-size-sm: calc(#{$font-size-base} - .0625rem);
    --nv-font-size-md: #{$font-size-base};
    --nv-font-size-lg: calc(#{$font-size-base} * 1.25);
    --nv-font-size-xxl: calc(#{$font-size-base} * 1.625);
}

/* Table */
figure.table .ck-table-resized {
    table-layout: fixed;
}

figure.table table {
    overflow: hidden;
}

figure.table td,
figure.table th {
    overflow-wrap: break-word;
    position: relative;
}

figure.table {
    margin: 5px auto 10px auto;
    display: table;
}

figure.table table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    height: 100%;
    border: 1px double var(--bs-border-color);
}

figure.table table td,
figure.table table th {
    min-width: 5px;
    padding: 7px;
    border: 1px solid var(--bs-border-color);
}

figure.table table th {
    font-weight: bold;
    border-bottom-width: 2px;
}

figure.table > figcaption {
    display: table-caption;
    caption-side: top;
    word-break: break-word;
    text-align: center;
    outline-offset: -1px;
    margin-top: 0;
}

/* Media */
figure.media {
    clear: both;
    margin: 5px 0 10px 0;
    display: block;
    min-width: 10px;
}

/* NV-Media */
figure.nv-media {
    clear: both;
    margin: 5px 0 10px 0;
    display: block;
    min-width: 10px;
}

figure.nv-media video,
figure.nv-media audio {
    max-width: 100%;
    margin: 0 auto;
    display: block;
}

/* Image */
img.image_resized {
    height: auto;
}

figure.image.image_resized {
    max-width: 100%;
    display: block;
    box-sizing: border-box;
}

figure.image.image_resized img {
    width: 100%;
}

figure.image.image_resized > figcaption {
    display: block;
}

figure.image {
    display: table!important; /* Important để không xung đột khi copy từ Google Docs */
    clear: both;
    text-align: center;
    margin: 5px auto 10px auto;
    min-width: 10px;
}

figure.image img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    min-width: 100%;
    height: auto;
}

figure.image-inline {
    display: inline-flex;
    max-width: 100%;
    align-items: flex-start;
}

figure.image-inline picture {
    display: flex;
}

figure.image-inline picture,
figure.image-inline img {
    flex-grow: 1;
    flex-shrink: 1;
    max-width: 100%;
}

figure.image > figcaption {
    display: table-caption;
    caption-side: bottom;
    word-break: break-word;
    padding: 7px 7px 0 7px;
    font-size: 13px;
    outline-offset: -1px;
    margin-top: 0;
}

/* Image Style */
.image-style-block-align-left,
.image-style-block-align-right {
    max-width: calc(100% - var(--nv-image-style-spacing));
}

.image-style-align-left,
.image-style-align-right {
    clear: none;
}

.image-style-side {
    float: right;
    margin-left: var(--nv-image-style-spacing);
    max-width: 50%;
}

.image-style-align-left {
    float: left;
    margin-right: var(--nv-image-style-spacing);
}

.image-style-align-center {
    margin-left: auto;
    margin-right: auto;
}

.image-style-align-right {
    float: right;
    margin-left: var(--nv-image-style-spacing);
}

.image-style-block-align-right {
    margin-right: 0;
    margin-left: auto;
}

.image-style-block-align-left {
    margin-left: 0;
    margin-right: auto;
}

p + .image-style-align-left,
p + .image-style-align-right,
p + .image-style-side {
    margin-top: 0;
}

.image-inline.image-style-align-left,
.image-inline.image-style-align-right {
    margin-top: var(--nv-inline-image-style-spacing);
    margin-bottom: var(--nv-inline-image-style-spacing);
}

.image-inline.image-style-align-left {
    margin-right: var(--nv-inline-image-style-spacing);
}

.image-inline.image-style-align-right {
    margin-left: var(--nv-inline-image-style-spacing);
}

/* Highlight */
.marker-yellow {
    background-color: var(--nv-highlight-marker-yellow);
}

.marker-green {
    background-color: var(--nv-highlight-marker-green);
}

.marker-pink {
    background-color: var(--nv-highlight-marker-pink);
}

.marker-blue {
    background-color: var(--nv-highlight-marker-blue);
}

.pen-red {
    color: var(--nv-highlight-pen-red);
    background-color: transparent;
}

.pen-green {
    color: var(--nv-highlight-pen-green);
    background-color: transparent;
}

/* Font size */
.text-tiny {
    font-size: var(--nv-font-size-xs);
}

.text-small {
    font-size: var(--nv-font-size-sm);
}

.text-big {
    font-size: var(--nv-font-size-lg);
}

.text-huge {
    font-size: var(--nv-font-size-xxl);
}

.is-invalid {
    --ck-color-base-border: #{$danger};
}
